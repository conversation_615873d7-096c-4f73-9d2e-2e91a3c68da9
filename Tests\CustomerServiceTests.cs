using NUnit.Framework;
using Microsoft.EntityFrameworkCore;
using DXApplication1.Models;
using DXApplication1.BusinessLogicLayer;
using DXApplication1.DataAccessLayer;

namespace DXApplication1.Tests
{
    /// <summary>
    /// اختبارات خدمة العملاء - Customer Service Tests
    /// </summary>
    [TestFixture]
    public class CustomerServiceTests
    {
        private SalesDbContext _context;
        private IUnitOfWork _unitOfWork;
        private ICustomerService _customerService;

        [SetUp]
        public void Setup()
        {
            // إعداد قاعدة بيانات في الذاكرة للاختبار - Setup in-memory database for testing
            var options = new DbContextOptionsBuilder<SalesDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new SalesDbContext(options);
            _unitOfWork = new UnitOfWork(_context);
            _customerService = new CustomerService(_unitOfWork, _context);

            // إضافة بيانات تجريبية - Add test data
            SeedTestData();
        }

        [TearDown]
        public void TearDown()
        {
            _context?.Dispose();
        }

        private void SeedTestData()
        {
            var customers = new[]
            {
                new Customer
                {
                    Id = 1,
                    CustomerName = "أحمد محمد",
                    Phone = "0501234567",
                    Email = "<EMAIL>",
                    CustomerType = CustomerType.Individual,
                    CreditLimit = 5000,
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    CreatedBy = "Test"
                },
                new Customer
                {
                    Id = 2,
                    CustomerName = "شركة التقنية",
                    CompanyName = "شركة التقنية المتقدمة",
                    Phone = "0112345678",
                    Email = "<EMAIL>",
                    CustomerType = CustomerType.Company,
                    CreditLimit = 20000,
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    CreatedBy = "Test"
                }
            };

            _context.Customers.AddRange(customers);
            _context.SaveChanges();
        }

        [Test]
        public async Task GetAllCustomersAsync_ReturnsAllActiveCustomers()
        {
            // Act
            var customers = await _customerService.GetAllCustomersAsync();

            // Assert
            Assert.IsNotNull(customers);
            Assert.AreEqual(2, customers.Count());
        }

        [Test]
        public async Task GetCustomerByIdAsync_ValidId_ReturnsCustomer()
        {
            // Arrange
            int customerId = 1;

            // Act
            var customer = await _customerService.GetCustomerByIdAsync(customerId);

            // Assert
            Assert.IsNotNull(customer);
            Assert.AreEqual(customerId, customer.Id);
            Assert.AreEqual("أحمد محمد", customer.CustomerName);
        }

        [Test]
        public async Task GetCustomerByIdAsync_InvalidId_ReturnsNull()
        {
            // Arrange
            int invalidId = 999;

            // Act
            var customer = await _customerService.GetCustomerByIdAsync(invalidId);

            // Assert
            Assert.IsNull(customer);
        }

        [Test]
        public async Task CreateCustomerAsync_ValidCustomer_ReturnsTrue()
        {
            // Arrange
            var newCustomer = new Customer
            {
                CustomerName = "فاطمة أحمد",
                Phone = "0509876543",
                Email = "<EMAIL>",
                CustomerType = CustomerType.Individual,
                CreditLimit = 3000,
                IsActive = true
            };

            // Act
            var result = await _customerService.CreateCustomerAsync(newCustomer);

            // Assert
            Assert.IsTrue(result);
            
            // التحقق من إضافة العميل - Verify customer was added
            var customers = await _customerService.GetAllCustomersAsync();
            Assert.AreEqual(3, customers.Count());
        }

        [Test]
        public async Task CreateCustomerAsync_NullCustomer_ReturnsFalse()
        {
            // Act
            var result = await _customerService.CreateCustomerAsync(null);

            // Assert
            Assert.IsFalse(result);
        }

        [Test]
        public async Task CreateCustomerAsync_EmptyName_ThrowsException()
        {
            // Arrange
            var invalidCustomer = new Customer
            {
                CustomerName = "", // اسم فارغ - Empty name
                Phone = "0501111111",
                CustomerType = CustomerType.Individual
            };

            // Act & Assert
            var ex = await Assert.ThrowsAsync<ArgumentException>(
                () => _customerService.CreateCustomerAsync(invalidCustomer));
            
            Assert.That(ex.Message, Contains.Substring("اسم العميل مطلوب"));
        }

        [Test]
        public async Task UpdateCustomerAsync_ValidCustomer_ReturnsTrue()
        {
            // Arrange
            var customer = await _customerService.GetCustomerByIdAsync(1);
            customer.CustomerName = "أحمد محمد المحدث";
            customer.Phone = "0501111111";

            // Act
            var result = await _customerService.UpdateCustomerAsync(customer);

            // Assert
            Assert.IsTrue(result);
            
            // التحقق من التحديث - Verify update
            var updatedCustomer = await _customerService.GetCustomerByIdAsync(1);
            Assert.AreEqual("أحمد محمد المحدث", updatedCustomer.CustomerName);
            Assert.AreEqual("0501111111", updatedCustomer.Phone);
        }

        [Test]
        public async Task DeleteCustomerAsync_ValidId_ReturnsTrue()
        {
            // Act
            var result = await _customerService.DeleteCustomerAsync(1);

            // Assert
            Assert.IsTrue(result);
            
            // التحقق من الحذف المنطقي - Verify soft delete
            var customer = await _customerService.GetCustomerByIdAsync(1);
            Assert.IsNull(customer); // Should not be returned as it's deleted
        }

        [Test]
        public async Task SearchCustomersAsync_ValidTerm_ReturnsMatchingCustomers()
        {
            // Act
            var customers = await _customerService.SearchCustomersAsync("أحمد");

            // Assert
            Assert.IsNotNull(customers);
            Assert.AreEqual(1, customers.Count());
            Assert.AreEqual("أحمد محمد", customers.First().CustomerName);
        }

        [Test]
        public async Task SearchCustomersAsync_EmptyTerm_ReturnsAllCustomers()
        {
            // Act
            var customers = await _customerService.SearchCustomersAsync("");

            // Assert
            Assert.IsNotNull(customers);
            Assert.AreEqual(2, customers.Count());
        }

        [Test]
        public async Task IsEmailAvailableAsync_NewEmail_ReturnsTrue()
        {
            // Act
            var isAvailable = await _customerService.IsEmailAvailableAsync("<EMAIL>");

            // Assert
            Assert.IsTrue(isAvailable);
        }

        [Test]
        public async Task IsEmailAvailableAsync_ExistingEmail_ReturnsFalse()
        {
            // Act
            var isAvailable = await _customerService.IsEmailAvailableAsync("<EMAIL>");

            // Assert
            Assert.IsFalse(isAvailable);
        }

        [Test]
        public void ValidateCustomerData_ValidCustomer_ReturnsTrue()
        {
            // Arrange
            var customer = new Customer
            {
                CustomerName = "عميل صحيح",
                Email = "<EMAIL>",
                CreditLimit = 1000,
                CustomerType = CustomerType.Individual
            };

            // Act
            var isValid = _customerService.ValidateCustomerData(customer, out List<string> errors);

            // Assert
            Assert.IsTrue(isValid);
            Assert.IsEmpty(errors);
        }

        [Test]
        public void ValidateCustomerData_InvalidCustomer_ReturnsFalse()
        {
            // Arrange
            var customer = new Customer
            {
                CustomerName = "", // اسم فارغ - Empty name
                Email = "invalid-email", // بريد إلكتروني غير صحيح - Invalid email
                CreditLimit = -100 // حد ائتمان سالب - Negative credit limit
            };

            // Act
            var isValid = _customerService.ValidateCustomerData(customer, out List<string> errors);

            // Assert
            Assert.IsFalse(isValid);
            Assert.IsNotEmpty(errors);
            Assert.That(errors.Count, Is.GreaterThan(0));
        }
    }
}
