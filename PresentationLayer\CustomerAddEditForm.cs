using DXApplication1.Models;
using DXApplication1.BusinessLogicLayer;
using DXApplication1.DataAccessLayer;
using DevExpress.XtraEditors;
using System.ComponentModel;

namespace DXApplication1.PresentationLayer
{
    /// <summary>
    /// نموذج إضافة/تعديل العميل - Customer Add/Edit Form
    /// </summary>
    public partial class CustomerAddEditForm : XtraForm
    {
        private Customer? _customer;
        private ICustomerService _customerService;
        private bool _isEditMode;

        // DevExpress Controls
        private TextEdit txtCustomerName;
        private TextEdit txtCompanyName;
        private TextEdit txtPhone;
        private TextEdit txtMobile;
        private TextEdit txtEmail;
        private MemoEdit txtAddress;
        private TextEdit txtCity;
        private TextEdit txtCountry;
        private TextEdit txtPostalCode;
        private TextEdit txtTaxNumber;
        private SpinEdit spnCreditLimit;
        private SpinEdit spnCurrentBalance;
        private ComboBoxEdit cmbCustomerType;
        private MemoEdit txtNotes;
        private CheckEdit chkIsActive;

        private SimpleButton btnSave;
        private SimpleButton btnCancel;

        // Labels
        private LabelControl lblCustomerName;
        private LabelControl lblCompanyName;
        private LabelControl lblPhone;
        private LabelControl lblMobile;
        private LabelControl lblEmail;
        private LabelControl lblAddress;
        private LabelControl lblCity;
        private LabelControl lblCountry;
        private LabelControl lblPostalCode;
        private LabelControl lblTaxNumber;
        private LabelControl lblCreditLimit;
        private LabelControl lblCurrentBalance;
        private LabelControl lblCustomerType;
        private LabelControl lblNotes;

        public CustomerAddEditForm(Customer? customer = null)
        {
            _customer = customer;
            _isEditMode = customer != null;

            InitializeComponent();
            InitializeServices();
            SetupForm();
            LoadData();
        }

        private void InitializeServices()
        {
            // إنشاء الخدمات - Initialize services
            var connectionString = System.Configuration.ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                ?? "Server=.;Database=FirstDB;Trusted_Connection=true;TrustServerCertificate=true;";
            var context = new SalesDbContext();
            var unitOfWork = new UnitOfWork(context);
            _customerService = new CustomerService(unitOfWork, context);
        }

        private void InitializeComponent()
        {
            // Initialize all controls
            this.txtCustomerName = new TextEdit();
            this.txtCompanyName = new TextEdit();
            this.txtPhone = new TextEdit();
            this.txtMobile = new TextEdit();
            this.txtEmail = new TextEdit();
            this.txtAddress = new MemoEdit();
            this.txtCity = new TextEdit();
            this.txtCountry = new TextEdit();
            this.txtPostalCode = new TextEdit();
            this.txtTaxNumber = new TextEdit();
            this.spnCreditLimit = new SpinEdit();
            this.spnCurrentBalance = new SpinEdit();
            this.cmbCustomerType = new ComboBoxEdit();
            this.txtNotes = new MemoEdit();
            this.chkIsActive = new CheckEdit();

            this.btnSave = new SimpleButton();
            this.btnCancel = new SimpleButton();

            // Initialize labels
            this.lblCustomerName = new LabelControl();
            this.lblCompanyName = new LabelControl();
            this.lblPhone = new LabelControl();
            this.lblMobile = new LabelControl();
            this.lblEmail = new LabelControl();
            this.lblAddress = new LabelControl();
            this.lblCity = new LabelControl();
            this.lblCountry = new LabelControl();
            this.lblPostalCode = new LabelControl();
            this.lblTaxNumber = new LabelControl();
            this.lblCreditLimit = new LabelControl();
            this.lblCurrentBalance = new LabelControl();
            this.lblCustomerType = new LabelControl();
            this.lblNotes = new LabelControl();

            this.SuspendLayout();

            // Form properties
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(600, 700);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = _isEditMode ? "تعديل العميل" : "إضافة عميل جديد";
            this.IconOptions.ShowIcon = false;

            // Layout controls
            int yPos = 20;
            int labelWidth = 120;
            int controlWidth = 200;
            int spacing = 35;

            // Customer Name
            SetupLabelAndControl(lblCustomerName, txtCustomerName, "اسم العميل:", 20, yPos, labelWidth, controlWidth, true);
            yPos += spacing;

            // Company Name
            SetupLabelAndControl(lblCompanyName, txtCompanyName, "اسم الشركة:", 20, yPos, labelWidth, controlWidth);
            yPos += spacing;

            // Phone
            SetupLabelAndControl(lblPhone, txtPhone, "الهاتف:", 20, yPos, labelWidth, controlWidth);
            yPos += spacing;

            // Mobile
            SetupLabelAndControl(lblMobile, txtMobile, "الجوال:", 20, yPos, labelWidth, controlWidth);
            yPos += spacing;

            // Email
            SetupLabelAndControl(lblEmail, txtEmail, "البريد الإلكتروني:", 20, yPos, labelWidth, controlWidth);
            yPos += spacing;

            // Customer Type
            SetupLabelAndControl(lblCustomerType, cmbCustomerType, "نوع العميل:", 20, yPos, labelWidth, controlWidth);
            yPos += spacing;

            // City
            SetupLabelAndControl(lblCity, txtCity, "المدينة:", 20, yPos, labelWidth, controlWidth);
            yPos += spacing;

            // Country
            SetupLabelAndControl(lblCountry, txtCountry, "الدولة:", 20, yPos, labelWidth, controlWidth);
            txtCountry.Text = "السعودية"; // Default value
            yPos += spacing;

            // Postal Code
            SetupLabelAndControl(lblPostalCode, txtPostalCode, "الرمز البريدي:", 20, yPos, labelWidth, controlWidth);
            yPos += spacing;

            // Tax Number
            SetupLabelAndControl(lblTaxNumber, txtTaxNumber, "الرقم الضريبي:", 20, yPos, labelWidth, controlWidth);
            yPos += spacing;

            // Credit Limit
            SetupLabelAndControl(lblCreditLimit, spnCreditLimit, "حد الائتمان:", 20, yPos, labelWidth, controlWidth);
            spnCreditLimit.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            spnCreditLimit.Properties.DisplayFormat.FormatString = "N2";
            spnCreditLimit.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            spnCreditLimit.Properties.EditFormat.FormatString = "N2";
            spnCreditLimit.Properties.MaxValue = *********;
            spnCreditLimit.Properties.MinValue = 0;
            yPos += spacing;

            // Current Balance (only in edit mode)
            if (_isEditMode)
            {
                SetupLabelAndControl(lblCurrentBalance, spnCurrentBalance, "الرصيد الحالي:", 20, yPos, labelWidth, controlWidth);
                spnCurrentBalance.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                spnCurrentBalance.Properties.DisplayFormat.FormatString = "N2";
                spnCurrentBalance.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                spnCurrentBalance.Properties.EditFormat.FormatString = "N2";
                spnCurrentBalance.Properties.MaxValue = *********;
                spnCurrentBalance.Properties.MinValue = -*********;
                spnCurrentBalance.Properties.ReadOnly = true; // Balance should be managed through transactions
                yPos += spacing;
            }

            // Address
            lblAddress.Text = "العنوان:";
            lblAddress.Location = new Point(20, yPos);
            lblAddress.Size = new Size(labelWidth, 21);
            txtAddress.Location = new Point(150, yPos);
            txtAddress.Size = new Size(controlWidth + 200, 60);
            this.Controls.Add(lblAddress);
            this.Controls.Add(txtAddress);
            yPos += 70;

            // Notes
            lblNotes.Text = "ملاحظات:";
            lblNotes.Location = new Point(20, yPos);
            lblNotes.Size = new Size(labelWidth, 21);
            txtNotes.Location = new Point(150, yPos);
            txtNotes.Size = new Size(controlWidth + 200, 60);
            this.Controls.Add(lblNotes);
            this.Controls.Add(txtNotes);
            yPos += 70;

            // Is Active
            chkIsActive.Text = "نشط";
            chkIsActive.Location = new Point(150, yPos);
            chkIsActive.Size = new Size(100, 21);
            chkIsActive.Checked = true;
            this.Controls.Add(chkIsActive);
            yPos += spacing;

            // Buttons
            btnSave.Text = "حفظ";
            btnSave.Location = new Point(400, yPos + 20);
            btnSave.Size = new Size(100, 35);
            btnSave.Appearance.BackColor = Color.FromArgb(0, 122, 204);
            btnSave.Appearance.ForeColor = Color.White;
            btnSave.Appearance.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnSave.Appearance.Options.UseBackColor = true;
            btnSave.Appearance.Options.UseForeColor = true;
            btnSave.Appearance.Options.UseFont = true;
            btnSave.Click += btnSave_Click;

            btnCancel.Text = "إلغاء";
            btnCancel.Location = new Point(280, yPos + 20);
            btnCancel.Size = new Size(100, 35);
            btnCancel.Appearance.Font = new Font("Segoe UI", 10F);
            btnCancel.Appearance.Options.UseFont = true;
            btnCancel.Click += btnCancel_Click;

            this.Controls.Add(btnSave);
            this.Controls.Add(btnCancel);

            // Adjust form height
            this.ClientSize = new Size(600, yPos + 80);

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void SetupLabelAndControl(LabelControl label, Control control, string labelText, int x, int y, int labelWidth, int controlWidth, bool required = false)
        {
            label.Text = labelText + (required ? " *" : "");
            label.Location = new Point(x, y + 3);
            label.Size = new Size(labelWidth, 21);
            if (required)
            {
                label.Appearance.ForeColor = Color.Red;
                label.Appearance.Options.UseForeColor = true;
            }

            control.Location = new Point(x + labelWidth + 10, y);
            control.Size = new Size(controlWidth, 24);

            this.Controls.Add(label);
            this.Controls.Add(control);
        }

        private void SetupForm()
        {
            // Setup customer type combo box
            cmbCustomerType.Properties.Items.Clear();
            cmbCustomerType.Properties.Items.Add(new { Text = "فرد", Value = CustomerType.Individual });
            cmbCustomerType.Properties.Items.Add(new { Text = "شركة", Value = CustomerType.Company });
            cmbCustomerType.Properties.DisplayMember = "Text";
            cmbCustomerType.Properties.ValueMember = "Value";
            cmbCustomerType.SelectedIndex = 0;
        }

        private void LoadData()
        {
            if (_customer != null)
            {
                txtCustomerName.Text = _customer.CustomerName;
                txtCompanyName.Text = _customer.CompanyName;
                txtPhone.Text = _customer.Phone;
                txtMobile.Text = _customer.Mobile;
                txtEmail.Text = _customer.Email;
                txtAddress.Text = _customer.Address;
                txtCity.Text = _customer.City;
                txtCountry.Text = _customer.Country;
                txtPostalCode.Text = _customer.PostalCode;
                txtTaxNumber.Text = _customer.TaxNumber;
                spnCreditLimit.Value = (decimal)_customer.CreditLimit;
                if (_isEditMode)
                    spnCurrentBalance.Value = (decimal)_customer.CurrentBalance;
                txtNotes.Text = _customer.Notes;
                chkIsActive.Checked = _customer.IsActive;

                // Set customer type
                for (int i = 0; i < cmbCustomerType.Properties.Items.Count; i++)
                {
                    var item = (dynamic)cmbCustomerType.Properties.Items[i];
                    if (item.Value == _customer.CustomerType)
                    {
                        cmbCustomerType.SelectedIndex = i;
                        break;
                    }
                }
            }
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm())
                    return;

                this.Cursor = Cursors.WaitCursor;
                btnSave.Enabled = false;

                var customer = CreateCustomerFromForm();
                bool success;

                if (_isEditMode)
                {
                    customer.Id = _customer!.Id;
                    success = await _customerService.UpdateCustomerAsync(customer);
                }
                else
                {
                    success = await _customerService.CreateCustomerAsync(customer);
                }

                if (success)
                {
                    XtraMessageBox.Show(
                        _isEditMode ? "تم تحديث العميل بنجاح" : "تم إضافة العميل بنجاح",
                        "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    XtraMessageBox.Show(
                        _isEditMode ? "فشل في تحديث العميل" : "فشل في إضافة العميل",
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"خطأ في حفظ البيانات:\n{ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                btnSave.Enabled = true;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateForm()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(txtCustomerName.Text))
                errors.Add("اسم العميل مطلوب");

            if (!string.IsNullOrWhiteSpace(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
                errors.Add("البريد الإلكتروني غير صحيح");

            if (spnCreditLimit.Value < 0)
                errors.Add("حد الائتمان لا يمكن أن يكون سالباً");

            if (errors.Count > 0)
            {
                XtraMessageBox.Show($"يرجى تصحيح الأخطاء التالية:\n{string.Join("\n", errors)}",
                    "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private Customer CreateCustomerFromForm()
        {
            var selectedCustomerType = (dynamic)cmbCustomerType.SelectedItem;

            return new Customer
            {
                CustomerName = txtCustomerName.Text.Trim(),
                CompanyName = string.IsNullOrWhiteSpace(txtCompanyName.Text) ? null : txtCompanyName.Text.Trim(),
                Phone = string.IsNullOrWhiteSpace(txtPhone.Text) ? null : txtPhone.Text.Trim(),
                Mobile = string.IsNullOrWhiteSpace(txtMobile.Text) ? null : txtMobile.Text.Trim(),
                Email = string.IsNullOrWhiteSpace(txtEmail.Text) ? null : txtEmail.Text.Trim(),
                Address = string.IsNullOrWhiteSpace(txtAddress.Text) ? null : txtAddress.Text.Trim(),
                City = string.IsNullOrWhiteSpace(txtCity.Text) ? null : txtCity.Text.Trim(),
                Country = string.IsNullOrWhiteSpace(txtCountry.Text) ? null : txtCountry.Text.Trim(),
                PostalCode = string.IsNullOrWhiteSpace(txtPostalCode.Text) ? null : txtPostalCode.Text.Trim(),
                TaxNumber = string.IsNullOrWhiteSpace(txtTaxNumber.Text) ? null : txtTaxNumber.Text.Trim(),
                CreditLimit = spnCreditLimit.Value,
                CurrentBalance = _isEditMode ? spnCurrentBalance.Value : 0,
                CustomerType = selectedCustomerType?.Value ?? CustomerType.Individual,
                Notes = string.IsNullOrWhiteSpace(txtNotes.Text) ? null : txtNotes.Text.Trim(),
                IsActive = chkIsActive.Checked
            };
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
